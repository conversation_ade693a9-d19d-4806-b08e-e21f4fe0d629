#include <QtTest/QtTest>
#include <QApplication>
#include <QWidget>
#include <QTextBrowser>
#include <QTreeWidget>
#include <QListWidget>
#include <QComboBox>
#include <QLineEdit>
#include <QSettings>
#include <QTemporaryFile>
#include <QDir>
#include <QStandardPaths>

#include "DocumentationViewer.h"
#include "Logger.h"

class TestDocumentationViewer : public QObject
{
    Q_OBJECT

private slots:
    void initTestCase();
    void cleanupTestCase();
    void init();
    void cleanup();

    // Core functionality tests
    void testInitialization();
    void testLoggerIntegration();
    void testMemoryManagement();
    
    // UI component tests
    void testUIComponents();
    void testToolbarButtons();
    void testSearchPanel();
    void testBookmarksPanel();
    void testTableOfContents();
    
    // Functionality tests
    void testZoomControls();
    void testSearchFunctionality();
    void testBookmarkManagement();
    void testHistoryNavigation();
    void testKeyboardShortcuts();
    
    // Content loading tests
    void testContentLoading();
    void testHTMLRendering();
    void testSyntaxHighlighting();
    
    // Error handling tests
    void testErrorHandling();
    void testInvalidContent();
    void testMemoryLeaks();

private:
    DocumentationViewer* m_viewer;
    QWidget* m_parent;
    QString m_testDataDir;
    QSettings* m_testSettings;
    
    void createTestHTML();
    void verifyNoMemoryLeaks();
};

void TestDocumentationViewer::initTestCase()
{
    // Initialize test environment
    m_testDataDir = QStandardPaths::writableLocation(QStandardPaths::TempLocation) + "/doc_viewer_test";
    QDir().mkpath(m_testDataDir);
    
    // Create test settings
    QString settingsPath = m_testDataDir + "/test_settings.ini";
    m_testSettings = new QSettings(settingsPath, QSettings::IniFormat);
    
    // Create test HTML files
    createTestHTML();
    
    qDebug() << "Test case initialized with data dir:" << m_testDataDir;
}

void TestDocumentationViewer::cleanupTestCase()
{
    delete m_testSettings;
    
    // Clean up test directory
    QDir testDir(m_testDataDir);
    testDir.removeRecursively();
    
    qDebug() << "Test case cleaned up";
}

void TestDocumentationViewer::init()
{
    // Create parent widget for each test
    m_parent = new QWidget();
    m_parent->resize(800, 600);
    
    // Create DocumentationViewer instance
    m_viewer = new DocumentationViewer(m_parent);
    
    // Ensure it's properly initialized
    QVERIFY(m_viewer != nullptr);
}

void TestDocumentationViewer::cleanup()
{
    // Clean up after each test
    if (m_viewer) {
        delete m_viewer;
        m_viewer = nullptr;
    }
    
    if (m_parent) {
        delete m_parent;
        m_parent = nullptr;
    }
}

void TestDocumentationViewer::testInitialization()
{
    // Test that the viewer initializes without crashing
    QVERIFY(m_viewer != nullptr);
    
    // Test that all essential components are created
    QVERIFY(m_viewer->findChild<QTextBrowser*>() != nullptr);
    QVERIFY(m_viewer->findChild<QTreeWidget*>() != nullptr);
    QVERIFY(m_viewer->findChild<QListWidget*>() != nullptr);
    
    // Test initial state
    QCOMPARE(m_viewer->getZoomLevel(), 100); // Default zoom
}

void TestDocumentationViewer::testLoggerIntegration()
{
    // Test that Logger integration doesn't cause crashes
    Logger* logger = Logger::instance();
    QVERIFY(logger != nullptr);
    
    // Test that we can safely destroy the viewer (this was the original bug)
    DocumentationViewer* testViewer = new DocumentationViewer();
    delete testViewer; // This should not crash
    
    // Logger should still be accessible
    QVERIFY(Logger::instance() != nullptr);
}

void TestDocumentationViewer::testMemoryManagement()
{
    // Test multiple create/destroy cycles
    for (int i = 0; i < 10; ++i) {
        DocumentationViewer* viewer = new DocumentationViewer();
        viewer->loadDocumentation("test content");
        delete viewer;
    }
    
    // Test should complete without crashes or memory leaks
    QVERIFY(true);
}

void TestDocumentationViewer::testUIComponents()
{
    // Test that all UI components are properly created
    QTextBrowser* browser = m_viewer->findChild<QTextBrowser*>();
    QVERIFY(browser != nullptr);
    
    QTreeWidget* tocTree = m_viewer->findChild<QTreeWidget*>();
    QVERIFY(tocTree != nullptr);
    
    QListWidget* bookmarksList = m_viewer->findChild<QListWidget*>();
    QVERIFY(bookmarksList != nullptr);
    
    QComboBox* zoomCombo = m_viewer->findChild<QComboBox*>();
    QVERIFY(zoomCombo != nullptr);
    
    QLineEdit* searchEdit = m_viewer->findChild<QLineEdit*>();
    QVERIFY(searchEdit != nullptr);
}

void TestDocumentationViewer::testToolbarButtons()
{
    // Test that toolbar buttons are created and functional
    auto buttons = m_viewer->findChildren<QAbstractButton*>();
    QVERIFY(buttons.size() > 0);
    
    // Test that buttons have proper tooltips
    bool hasBackButton = false;
    bool hasForwardButton = false;
    bool hasPrintButton = false;
    
    for (auto* button : buttons) {
        QString tooltip = button->toolTip();
        if (tooltip.contains("Back")) hasBackButton = true;
        if (tooltip.contains("Forward")) hasForwardButton = true;
        if (tooltip.contains("Print")) hasPrintButton = true;
    }
    
    QVERIFY(hasBackButton);
    QVERIFY(hasForwardButton);
    QVERIFY(hasPrintButton);
}

void TestDocumentationViewer::testSearchPanel()
{
    QLineEdit* searchEdit = m_viewer->findChild<QLineEdit*>();
    QVERIFY(searchEdit != nullptr);
    
    // Test search functionality
    QString testContent = "<html><body><h1>Test Header</h1><p>This is test content with searchable text.</p></body></html>";
    m_viewer->loadDocumentation(testContent);
    
    // Simulate search
    searchEdit->setText("searchable");
    m_viewer->searchText("searchable", false, false);
    
    // Search should complete without errors
    QVERIFY(true);
}

void TestDocumentationViewer::testBookmarksPanel()
{
    QListWidget* bookmarksList = m_viewer->findChild<QListWidget*>();
    QVERIFY(bookmarksList != nullptr);
    
    // Test adding bookmark
    QString testUrl = "test://bookmark";
    m_viewer->addBookmark(testUrl, "Test Bookmark");
    
    // Bookmark should be added
    QVERIFY(bookmarksList->count() > 0);
    
    // Test removing bookmark
    m_viewer->removeBookmark(testUrl);
    
    // Test should complete without errors
    QVERIFY(true);
}

void TestDocumentationViewer::testTableOfContents()
{
    QTreeWidget* tocTree = m_viewer->findChild<QTreeWidget*>();
    QVERIFY(tocTree != nullptr);
    
    // Load content with headers
    QString htmlWithHeaders = 
        "<html><body>"
        "<h1>Chapter 1</h1>"
        "<h2>Section 1.1</h2>"
        "<h2>Section 1.2</h2>"
        "<h1>Chapter 2</h1>"
        "</body></html>";
    
    m_viewer->loadDocumentation(htmlWithHeaders);
    
    // TOC should be populated
    QVERIFY(tocTree->topLevelItemCount() > 0);
}

void TestDocumentationViewer::testZoomControls()
{
    // Test zoom functionality
    int initialZoom = m_viewer->getZoomLevel();
    QCOMPARE(initialZoom, 100);
    
    // Test zoom in
    m_viewer->zoomIn();
    QVERIFY(m_viewer->getZoomLevel() > initialZoom);
    
    // Test zoom out
    m_viewer->zoomOut();
    QVERIFY(m_viewer->getZoomLevel() < m_viewer->getZoomLevel());
    
    // Test reset zoom
    m_viewer->resetZoom();
    QCOMPARE(m_viewer->getZoomLevel(), 100);
    
    // Test set zoom level
    m_viewer->setZoomLevel(150);
    QCOMPARE(m_viewer->getZoomLevel(), 150);
}

void TestDocumentationViewer::testSearchFunctionality()
{
    QString testContent = 
        "<html><body>"
        "<p>This is the first paragraph with some text.</p>"
        "<p>This is the second paragraph with different text.</p>"
        "<p>Another paragraph with more content.</p>"
        "</body></html>";
    
    m_viewer->loadDocumentation(testContent);
    
    // Test case-sensitive search
    m_viewer->searchText("paragraph", true, false);
    
    // Test case-insensitive search
    m_viewer->searchText("PARAGRAPH", false, false);
    
    // Test whole word search
    m_viewer->searchText("text", false, true);
    
    // All searches should complete without errors
    QVERIFY(true);
}

void TestDocumentationViewer::testBookmarkManagement()
{
    // Test bookmark persistence
    QString bookmark1 = "test://url1";
    QString bookmark2 = "test://url2";
    
    m_viewer->addBookmark(bookmark1, "Bookmark 1");
    m_viewer->addBookmark(bookmark2, "Bookmark 2");
    
    // Save bookmarks
    m_viewer->saveBookmarks();
    
    // Create new viewer and load bookmarks
    DocumentationViewer* newViewer = new DocumentationViewer();
    newViewer->loadBookmarks();
    
    // Bookmarks should be loaded
    QListWidget* bookmarksList = newViewer->findChild<QListWidget*>();
    QVERIFY(bookmarksList != nullptr);
    
    delete newViewer;
}

void TestDocumentationViewer::testHistoryNavigation()
{
    // Load multiple pages to create history
    m_viewer->loadDocumentation("<html><body><h1>Page 1</h1></body></html>");
    m_viewer->loadDocumentation("<html><body><h1>Page 2</h1></body></html>");
    m_viewer->loadDocumentation("<html><body><h1>Page 3</h1></body></html>");
    
    // Test navigation
    m_viewer->goBack();
    m_viewer->goBack();
    m_viewer->goForward();
    
    // Navigation should complete without errors
    QVERIFY(true);
}

void TestDocumentationViewer::testKeyboardShortcuts()
{
    // Test that keyboard shortcuts are properly set up
    auto shortcuts = m_viewer->findChildren<QShortcut*>();
    QVERIFY(shortcuts.size() > 0);
    
    // Test should complete without errors
    QVERIFY(true);
}

void TestDocumentationViewer::testContentLoading()
{
    QString testContent = "<html><body><h1>Test Content</h1><p>This is a test.</p></body></html>";
    
    bool result = m_viewer->loadDocumentation(testContent);
    QVERIFY(result);
    
    // Content should be loaded in the browser
    QTextBrowser* browser = m_viewer->findChild<QTextBrowser*>();
    QVERIFY(browser != nullptr);
    QVERIFY(!browser->toPlainText().isEmpty());
}

void TestDocumentationViewer::testHTMLRendering()
{
    QString htmlContent = 
        "<html><head><title>Test</title></head>"
        "<body>"
        "<h1>Main Title</h1>"
        "<p>Paragraph with <strong>bold</strong> and <em>italic</em> text.</p>"
        "<ul><li>List item 1</li><li>List item 2</li></ul>"
        "</body></html>";
    
    m_viewer->loadDocumentation(htmlContent);
    
    // HTML should be rendered properly
    QTextBrowser* browser = m_viewer->findChild<QTextBrowser*>();
    QVERIFY(browser != nullptr);
    QVERIFY(browser->toHtml().contains("Main Title"));
}

void TestDocumentationViewer::testSyntaxHighlighting()
{
    QString codeContent = 
        "<html><body>"
        "<pre><code>"
        "function test() {\n"
        "    return \"Hello World\";\n"
        "}"
        "</code></pre>"
        "</body></html>";
    
    m_viewer->loadDocumentation(codeContent);
    
    // Syntax highlighter should be applied
    QTextBrowser* browser = m_viewer->findChild<QTextBrowser*>();
    QVERIFY(browser != nullptr);
    QVERIFY(browser->document() != nullptr);
}

void TestDocumentationViewer::testErrorHandling()
{
    // Test loading invalid content
    bool result = m_viewer->loadDocumentation("");
    // Should handle empty content gracefully
    
    // Test loading non-existent file
    result = m_viewer->loadDocumentation("non_existent_file.html");
    // Should handle missing files gracefully
    
    // Test should complete without crashes
    QVERIFY(true);
}

void TestDocumentationViewer::testInvalidContent()
{
    // Test malformed HTML
    QString malformedHTML = "<html><body><h1>Unclosed header<p>Missing closing tags";
    m_viewer->loadDocumentation(malformedHTML);
    
    // Should handle malformed HTML gracefully
    QVERIFY(true);
}

void TestDocumentationViewer::testMemoryLeaks()
{
    // Test repeated operations that could cause memory leaks
    for (int i = 0; i < 100; ++i) {
        m_viewer->loadDocumentation("<html><body><h1>Test " + QString::number(i) + "</h1></body></html>");
        m_viewer->searchText("Test", false, false);
        m_viewer->addBookmark("test://url" + QString::number(i), "Test " + QString::number(i));
    }
    
    // Clear all bookmarks
    for (int i = 0; i < 100; ++i) {
        m_viewer->removeBookmark("test://url" + QString::number(i));
    }
    
    // Test should complete without excessive memory usage
    QVERIFY(true);
}

void TestDocumentationViewer::createTestHTML()
{
    QString testHTML = 
        "<html><head><title>Test Documentation</title></head>"
        "<body>"
        "<h1>Test Documentation</h1>"
        "<h2>Introduction</h2>"
        "<p>This is test documentation content.</p>"
        "<h2>Features</h2>"
        "<ul>"
        "<li>Feature 1</li>"
        "<li>Feature 2</li>"
        "</ul>"
        "<h2>Code Examples</h2>"
        "<pre><code>"
        "function example() {\n"
        "    console.log('Hello World');\n"
        "}"
        "</code></pre>"
        "</body></html>";
    
    QFile testFile(m_testDataDir + "/test.html");
    if (testFile.open(QIODevice::WriteOnly | QIODevice::Text)) {
        QTextStream stream(&testFile);
        stream << testHTML;
    }
}

QTEST_MAIN(TestDocumentationViewer)
#include "test_documentation_viewer.moc"
