# Test configuration for Qt PDF Viewer
cmake_minimum_required(VERSION 3.16)

# Enable testing
enable_testing()

# Find Qt Test module
find_package(Qt6 REQUIRED COMPONENTS Test Core Widgets Gui Concurrent PrintSupport)

# Include directories
include_directories(${CMAKE_SOURCE_DIR}/include)

# Common test libraries and dependencies
set(TEST_LIBRARIES
    Qt6::Test
    Qt6::Core
    Qt6::Widgets
    Qt6::Gui
    Qt6::Concurrent
    Qt6::PrintSupport
    PkgConfig::POPPLER_CPP
    PkgConfig::POPPLER_QT6
)

# Test source files from main project (needed for testing)
set(MAIN_SOURCES
    ${CMAKE_SOURCE_DIR}/src/Annotation.cpp
    ${CMAKE_SOURCE_DIR}/src/AnnotationManager.cpp
    ${CMAKE_SOURCE_DIR}/src/AnnotationCommand.cpp
    ${CMAKE_SOURCE_DIR}/src/AnnotationClipboard.cpp
    ${CMAKE_SOURCE_DIR}/src/PdfController.cpp
    ${CMAKE_SOURCE_DIR}/src/DocumentTab.cpp
    ${CMAKE_SOURCE_DIR}/src/AnnotationOverlay.cpp
    ${CMAKE_SOURCE_DIR}/src/AnnotationToolbar.cpp
    ${CMAKE_SOURCE_DIR}/src/AnnotationPropertiesDialog.cpp
    ${CMAKE_SOURCE_DIR}/src/AnnotationSearchDialog.cpp
    ${CMAKE_SOURCE_DIR}/src/TextSelectionOverlay.cpp
    ${CMAKE_SOURCE_DIR}/src/AdvancedZoomManager.cpp
    ${CMAKE_SOURCE_DIR}/src/DocumentationViewer.cpp
    ${CMAKE_SOURCE_DIR}/src/DocumentationSyntaxHighlighter.cpp
    ${CMAKE_SOURCE_DIR}/src/Logger.cpp
)

# Header files from main project (needed for MOC processing)
set(MAIN_HEADERS
    ${CMAKE_SOURCE_DIR}/include/Annotation.h
    ${CMAKE_SOURCE_DIR}/include/AnnotationManager.h
    ${CMAKE_SOURCE_DIR}/include/AnnotationCommand.h
    ${CMAKE_SOURCE_DIR}/include/AnnotationClipboard.h
    ${CMAKE_SOURCE_DIR}/include/PdfController.h
    ${CMAKE_SOURCE_DIR}/include/DocumentTab.h
    ${CMAKE_SOURCE_DIR}/include/AnnotationOverlay.h
    ${CMAKE_SOURCE_DIR}/include/AnnotationToolbar.h
    ${CMAKE_SOURCE_DIR}/include/AnnotationPropertiesDialog.h
    ${CMAKE_SOURCE_DIR}/include/AnnotationSearchDialog.h
    ${CMAKE_SOURCE_DIR}/include/TextSelectionOverlay.h
    ${CMAKE_SOURCE_DIR}/include/AdvancedZoomManager.h
    ${CMAKE_SOURCE_DIR}/include/DocumentationViewer.h
    ${CMAKE_SOURCE_DIR}/include/DocumentationSyntaxHighlighter.h
    ${CMAKE_SOURCE_DIR}/include/Logger.h
)

# Function to create a test executable
function(add_qt_test test_name test_source)
    add_executable(${test_name} ${test_source} ${MAIN_SOURCES} ${MAIN_HEADERS})
    target_link_libraries(${test_name} ${TEST_LIBRARIES})

    # Include directories for the target
    target_include_directories(${test_name} PRIVATE
        ${CMAKE_SOURCE_DIR}/include
        ${CMAKE_CURRENT_BINARY_DIR}
    )

    # Set up Qt MOC for test files
    set_target_properties(${test_name} PROPERTIES
        AUTOMOC ON
        AUTORCC ON
        AUTOUIC ON
    )

    # Add the test to CTest
    add_test(NAME ${test_name} COMMAND ${test_name})

    # Set test properties
    set_tests_properties(${test_name} PROPERTIES
        TIMEOUT 30
        WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
    )
endfunction()

# Add individual test executables
add_qt_test(test_annotation test_annotation.cpp)
add_qt_test(test_annotation_manager test_annotation_manager.cpp)
add_qt_test(test_pdf_controller test_pdf_controller.cpp)
add_qt_test(test_document_tab test_document_tab.cpp)
add_qt_test(test_integration test_integration.cpp)
add_qt_test(test_text_selection test_text_selection.cpp)
add_qt_test(test_documentation_viewer test_documentation_viewer.cpp)

# Create a custom target to run all tests
add_custom_target(run_tests
    COMMAND ${CMAKE_CTEST_COMMAND} --output-on-failure --verbose
    DEPENDS test_annotation test_annotation_manager test_pdf_controller test_document_tab test_integration test_text_selection test_documentation_viewer
    WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
)

# Copy test data files to build directory
file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/data DESTINATION ${CMAKE_CURRENT_BINARY_DIR})
